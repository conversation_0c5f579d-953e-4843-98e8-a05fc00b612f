# This is a config for E2B sandbox template.
# You can use template ID (t01p49s8glt6jzii4hmu) to create a sandbox:

# Python SDK
# from e2b import Sandbox, AsyncSandbox
# sandbox = Sandbox("t01p49s8glt6jzii4hmu") # Sync sandbox
# sandbox = await AsyncSandbox.create("t01p49s8glt6jzii4hmu") # Async sandbox

# JS SDK
# import { Sandbox } from 'e2b'
# const sandbox = await Sandbox.create('t01p49s8glt6jzii4hmu')

team_id = ""
memory_mb = 512
cpu_count = 1
template_name = ""
dockerfile = "e2b.Dockerfile"
template_id = ""
