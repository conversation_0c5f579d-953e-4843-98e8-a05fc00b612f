{"name": "vite-shadcn-template-libra", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "daytona:build": "docker buildx build --platform linux/amd64 -t vite-shadcn-template-libra:1.0.0 -f Dockerfile .", "daytona:push": "daytona snapshot push vite-shadcn-template-libra:1.0.0 --entrypoint 'bun dev --host 0.0.0.0' --name vite-shadcn-template-libra:1.0.0 --cpu 1 --memory 1 --disk 3", "e2b:build": "e2b template build --cpu-count 2 -n 'vite-shadcn-template-libra' --memory-mb 1024 -c '/compile_page.sh'", "generate-structure": "bun generate-structure.ts"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-navigation-menu": "^1.2.13", "tw-animate-css": "^1.3.5", "class-variance-authority": "^0.7.1", "cmdk": "^0.2.1", "lucide-react": "^0.486.0", "next-themes": "^0.4.6", "react-day-picker": "^9.8.0", "react-hook-form": "^7.61.0", "sonner": "^2.0.6", "input-otp": "^1.4.2", "@splinetool/react-spline": "^4.1.0", "react-router-dom": "^7.7.0", "tailwind-merge": "^3.3.1", "zod": "^3.25.76", "@hookform/resolvers": "^5.1.1", "hono": "^4.8.5", "@tanstack/react-query": "^5.83.0", "recharts": "^2.15.4", "vaul": "^1.1.2", "react": "^19.1.0", "react-dom": "^19.1.0", "embla-carousel-react": "^8.6.0", "motion": "^12.23.9"}, "devDependencies": {"@types/node": "^22.16.5", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@tailwindcss/vite": "^4.1.11", "@vitejs/plugin-react-swc": "^3.11.0", "date-fns": "^4.1.0", "clsx": "^2.1.1", "postcss": "^8.5.6", "autoprefixer": "^10.4.21", "tailwindcss": "^4.1.11", "tailwindcss-animate": "^1.0.7", "typescript": "^5.8.3", "vite": "^6.3.5", "@cloudflare/vite-plugin": "^1.10.0", "wrangler": "^4.25.1", "sandbox-tagger": "^0.0.2", "@cloudflare/workers-types": "^4.20250724.0"}}