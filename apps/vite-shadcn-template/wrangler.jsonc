{
  "$schema": "../../node_modules/wrangler/config-schema.json",
  "name": "vite-shadcn-template",
  "main": "src/worker.ts",
  "compatibility_date": "2025-07-24",
  "compatibility_flags": [
    "nodejs_compat",
    "global_fetch_strictly_public"
  ],
  "assets": {
    "directory": "./dist/",
    "not_found_handling": "single-page-application",
    "binding": "ASSETS"
  },
  "minify": true,
  "placement": { "mode": "smart" },
  // "observability": {
  //   "enabled": true
  // }
}
