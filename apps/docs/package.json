{"name": "@libra/docs", "version": "1.0.0", "private": true, "scripts": {"build": "next build", "dev": "bun next dev --turbo -p 3003", "cf-typegen": "wrangler types --env-interface CloudflareEnv cloudflare-env.d.ts", "update": "bun update"}, "dependencies": {"fumadocs-core": "^15.6.6", "fumadocs-mdx": "^11.7.1", "fumadocs-ui": "^15.6.6", "motion": "^12.23.9", "octokit": "^5.0.3"}, "devDependencies": {"@types/mdx": "^2.0.13"}}