{
  "$schema": "../../node_modules/wrangler/config-schema.json",
  "name": "libra-screenshot",
  "main": "src/index.ts",
  "compatibility_date": "2025-07-17",
  "compatibility_flags": ["nodejs_compat", "global_fetch_strictly_public"],
  "assets": {
    "binding": "ASSETS",
    "directory": "public"
  },
  "routes": [
    {
      "pattern": "screenshot.libra.dev",
      "custom_domain": true
    }
  ],
  "minify": true,
  "placement": { "mode": "smart" },
  "limits": {
    "cpu_ms": 300000
  },
  "d1_databases": [
    {
      "binding": "DATABASE",
      "database_name": "libra",
      "database_id": "{{DATABASE_ID}}"
    }
  ],
  "hyperdrive": [
    {
      "binding": "HYPERDRIVE",
      "id": "{{HYPERDRIVE_ID}}"
    }
  ],
  "kv_namespaces": [
    {
      "binding": "KV",
      "id": "{{KV_NAMESPACE_ID}}"
    }
  ],
  "queues": {
    "consumers": [
      {
        "queue": "screenshot-queue",
        "max_batch_size": 1,
        "max_batch_timeout": 0,
        "max_retries": 2,
        "max_concurrency": 5,
        "dead_letter_queue": "screenshot-dlq"
      }
    ],
    "producers": [
      {
        "queue": "screenshot-queue",
        "binding": "SCREENSHOT_QUEUE"
      },
      {
        "queue": "screenshot-dlq",
        "binding": "SCREENSHOT_DLQ"
      }
    ]
  },

  "observability": {
    "enabled": true,
    "head_sampling_rate": 1
  },
  "vars": {
     // Environment configuration
     "ENVIRONMENT": "{{ENVIRONMENT}}",
     // Core application configuration
     "NEXT_PUBLIC_APP_URL": "{{NEXT_PUBLIC_APP_URL}}",
     "NEXT_PUBLIC_CDN_URL": "{{NEXT_PUBLIC_CDN_URL}}",
     // Authentication configuration
     "BETTER_AUTH_SECRET": "{{BETTER_AUTH_SECRET}}",
     "BETTER_GITHUB_CLIENT_ID": "{{BETTER_GITHUB_CLIENT_ID}}",
     "BETTER_GITHUB_CLIENT_SECRET": "{{BETTER_GITHUB_CLIENT_SECRET}}",
     // Security configuration
     "TURNSTILE_SECRET_KEY": "{{TURNSTILE_SECRET_KEY}}",
     // Payment configuration (optional)
     "STRIPE_SECRET_KEY": "{{STRIPE_SECRET_KEY}}",
     "STRIPE_WEBHOOK_SECRET": "{{STRIPE_WEBHOOK_SECRET}}",
     // Admin configuration (optional)
     "ADMIN_USER_IDS": "{{ADMIN_USER_IDS}}",
     // Database configuration
     "POSTGRES_URL": "{{POSTGRES_URL}}",
     "DATABASE_ID": "{{DATABASE_ID}}",
     // Cloudflare configuration
     "CLOUDFLARE_ACCOUNT_ID": "{{CLOUDFLARE_ACCOUNT_ID}}",
     "CLOUDFLARE_API_TOKEN": "{{CLOUDFLARE_API_TOKEN}}",
     "CLOUDFLARE_ZONE_ID": "{{CLOUDFLARE_ZONE_ID}}",
     "CLOUDFLARE_AIGATEWAY_NAME": "{{CLOUDFLARE_AIGATEWAY_NAME}}",
     // Email service (optional)
     "RESEND_API_KEY": "{{RESEND_API_KEY}}",
     "RESEND_FROM": "{{RESEND_FROM}}",
     "LOG_LEVEL": "{{LOG_LEVEL}}",
     // Sandbox provider configuration
     "E2B_API_KEY": "{{E2B_API_KEY}}",
     "DAYTONA_API_KEY": "{{DAYTONA_API_KEY}}",
     "SANDBOX_BUILDER_DEFAULT_PROVIDER": "{{SANDBOX_BUILDER_DEFAULT_PROVIDER}}",
     // Application URLs
     "NEXT_PUBLIC_DISPATCHER_URL": "{{NEXT_PUBLIC_DISPATCHER_URL}}",
   }
}
