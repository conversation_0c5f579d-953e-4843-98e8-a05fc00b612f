{"extends": "@libra/typescript-config/base.json", "compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/types/*": ["./src/types/*"], "@/queue/*": ["./src/queue/*"], "@/deployment/*": ["./src/deployment/*"], "@/storage/*": ["./src/storage/*"], "@/api/*": ["./src/api/*"], "@/utils/*": ["./src/utils/*"]}, "types": ["@cloudflare/workers-types", "node"]}, "include": ["src/**/*", "worker-configuration.d.ts"], "exclude": ["node_modules", "dist", ".wrangler"]}